import { MainLayout } from "@/widgets/layouts/main-layout"
import { QuickAccessGrid } from "@/widgets/quick-access/quick-access-grid"
import { RecentLessons } from "@/widgets/lessons/recent-lessons"
import { KnowledgeBaseCards } from "@/widgets/knowledge-base/knowledge-base-cards"

export default function DashboardPage() {
  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">My Secret Notebook</h1>
          <p className="text-xl text-gray-400">Personal collection of techniques and strategies</p>
        </div>

        <div className="grid grid-cols-1 gap-8">
          <QuickAccessGrid />
          <RecentLessons />
          <KnowledgeBaseCards />
        </div>
      </div>
    </MainLayout>
  )
}

import { MainLayout } from "@/widgets/layouts/main-layout"
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/shared/ui/card"
import { Button } from "@/shared/ui/button"
import { Badge } from "@/shared/ui/badge"
import { ArrowLeft, CheckCircle, BookOpen, Clock, Target } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { knowledgeBases } from "@/entities/knowledge-base/model/knowledge-base"
import { LessonNotes } from "@/features/notes/ui/lesson-notes"
import { FavoriteButton } from "@/features/favorites/ui/favorite-button"

export default function LessonPage({ params }: { params: { id: string; category: string; lesson: string } }) {
  const kb = knowledgeBases.find((k) => k.id === params.id)
  const category = kb?.categories.find((c) => c.id === params.category)
  const lesson = category?.lessons.find((l) => l.id === params.lesson)

  if (!kb || !category || !lesson) {
    notFound()
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Intermediate":
        return "bg-yellow-900/20 text-yellow-400 border-yellow-800"
      case "Advanced":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      case "Expert":
        return "bg-red-900/20 text-red-400 border-red-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Theory":
        return "bg-blue-900/20 text-blue-400 border-blue-800"
      case "Practical":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Case Study":
        return "bg-purple-900/20 text-purple-400 border-purple-800"
      case "Exercise":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <div className="flex items-center gap-2 text-sm text-gray-400 mb-4">
            <Link href="/" className="hover:text-white transition-colors">
              Vault
            </Link>
            <span>→</span>
            <Link href={`/knowledge-base/${kb.id}`} className="hover:text-white transition-colors">
              {kb.title}
            </Link>
            <span>→</span>
            <Link href={`/knowledge-base/${kb.id}/${category.id}`} className="hover:text-white transition-colors">
              {category.title}
            </Link>
            <span>→</span>
            <span className="text-white">{lesson.title}</span>
          </div>

          <Link href={`/knowledge-base/${kb.id}/${category.id}`}>
            <Button variant="ghost" className="text-gray-400 hover:text-white mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to {category.title}
            </Button>
          </Link>
        </div>

        {/* Lesson Header */}
        <div className="mb-8">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <h1 className="text-4xl font-bold text-white">{lesson.title}</h1>
                <FavoriteButton lessonId={lesson.id} initialState={lesson.isFavorite} />
              </div>
              <p className="text-xl text-gray-400 mb-6">{lesson.description}</p>

              <div className="flex items-center gap-4 mb-6">
                <Badge className={getTypeColor(lesson.type)}>{lesson.type}</Badge>
                <Badge className={getDifficultyColor(lesson.difficulty)}>{lesson.difficulty}</Badge>
                <div className="flex items-center gap-1 text-gray-400">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">{lesson.duration}</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-6">
                {lesson.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="border-gray-600 text-gray-400">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          <div className="flex gap-4">
            <Button className="bg-red-600 hover:bg-red-700 text-white">
              <CheckCircle className="w-4 h-4 mr-2" />
              Mark as Complete
            </Button>
            <Button variant="outline" className="border-gray-600 text-gray-300">
              <Target className="w-4 h-4 mr-2" />
              Practice Exercise
            </Button>
          </div>
        </div>

        {/* Lesson Content */}
        <Card className="bg-gray-900/50 border-gray-800 mb-8">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-red-400" />
              Lesson Content
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-invert max-w-none">
              <div
                className="text-gray-300 leading-relaxed"
                dangerouslySetInnerHTML={{
                  __html: lesson.content
                    .replace(/^# /gm, '<h1 class="text-3xl font-bold text-white mb-4 mt-6">')
                    .replace(/^## /gm, '<h2 class="text-2xl font-semibold text-white mb-3 mt-5">')
                    .replace(/^### /gm, '<h3 class="text-xl font-medium text-white mb-2 mt-4">')
                    .replace(/^- /gm, '<li class="mb-1">')
                    .replace(/^\d+\. /gm, '<li class="mb-1">')
                    .replace(/\*\*(.*?)\*\*/g, '<strong class="text-red-400">$1</strong>')
                    .replace(/\n\n/g, '</p><p class="mb-4">')
                    .replace(/^(?!<[h|l])/gm, '<p class="mb-4">')
                    .replace(/<p class="mb-4"><\/p>/g, ""),
                }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Personal Notes Section */}
        <LessonNotes lessonId={lesson.id} lessonTitle={lesson.title} />

        {/* Navigation to Next/Previous Lessons */}
        <div className="mt-12 flex justify-between">
          <div>{/* Previous lesson logic would go here */}</div>
          <div>{/* Next lesson logic would go here */}</div>
        </div>
      </div>
    </MainLayout>
  )
}

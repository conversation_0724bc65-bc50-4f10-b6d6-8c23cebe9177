import { MainLayout } from "@/widgets/layouts/main-layout"
import { Card, CardDescription, CardHeader, CardTitle } from "@/shared/ui/card"
import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Badge } from "@/shared/ui/badge"
import { ArrowLeft, CheckCircle, Lock, BookOpen } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { knowledgeBases } from "@/entities/knowledge-base/model/knowledge-base"

export default function CategoryPage({ params }: { params: { id: string; category: string } }) {
  const kb = knowledgeBases.find((k) => k.id === params.id)
  const category = kb?.categories.find((c) => c.id === params.category)

  if (!kb || !category) {
    notFound()
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Theory":
        return "bg-blue-900/20 text-blue-400 border-blue-800"
      case "Practical":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Case Study":
        return "bg-purple-900/20 text-purple-400 border-purple-800"
      case "Exercise":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Intermediate":
        return "bg-yellow-900/20 text-yellow-400 border-yellow-800"
      case "Advanced":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      case "Expert":
        return "bg-red-900/20 text-red-400 border-red-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-8">
          <div className="flex items-center gap-2 text-sm text-gray-400 mb-4">
            <Link href="/" className="hover:text-white transition-colors">
              Vault
            </Link>
            <span>→</span>
            <Link href={`/knowledge-base/${kb.id}`} className="hover:text-white transition-colors">
              {kb.title}
            </Link>
            <span>→</span>
            <span className="text-white">{category.title}</span>
          </div>

          <Link href={`/knowledge-base/${kb.id}`}>
            <Button variant="ghost" className="text-gray-400 hover:text-white mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to {kb.title}
            </Button>
          </Link>
        </div>

        {/* Category Header */}
        <div className="mb-12">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <h1 className="text-5xl font-bold text-white mb-4">{category.title}</h1>
              <p className="text-xl text-gray-400 mb-6 max-w-3xl">{category.description}</p>

              <div className="flex items-center gap-6 text-sm text-gray-500 mb-6">
                <span className="flex items-center gap-2">
                  <BookOpen className="w-4 h-4" />
                  {category.lessons.length} Lessons
                </span>
                <Badge className={getDifficultyColor(category.difficulty)}>{category.difficulty}</Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Lessons Grid */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-white mb-6">Lessons & Techniques</h2>

          <div className="grid gap-6">
            {category.lessons.map((lesson, index) => (
              <Link key={lesson.id} href={`/knowledge-base/${kb.id}/${category.id}/${lesson.id}`}>
                <Card className="bg-gray-900/50 border-gray-800 hover:border-red-500/50 transition-all duration-300 group cursor-pointer hover-lift">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-6 flex-1">
                        <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-800 text-gray-400 text-lg font-bold group-hover:bg-red-600 group-hover:text-white transition-all">
                          {index + 1}
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <CardTitle className="text-white group-hover:text-red-400 transition-colors text-xl">
                              {lesson.title}
                            </CardTitle>
                            {lesson.completed && <CheckCircle className="w-5 h-5 text-green-400" />}
                          </div>

                          <CardDescription className="text-gray-400 text-base mb-4 leading-relaxed">
                            {lesson.description}
                          </CardDescription>

                          <div className="flex items-center gap-4 mb-4">
                            <Badge className={getTypeColor(lesson.type)}>{lesson.type}</Badge>
                            <Badge className={getDifficultyColor(lesson.difficulty)}>{lesson.difficulty}</Badge>
                          </div>

                          <div className="flex flex-wrap gap-2">
                            {lesson.tags.slice(0, 4).map((tag) => (
                              <Badge key={tag} variant="outline" className="border-gray-600 text-gray-400 text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {lesson.tags.length > 4 && (
                              <Badge variant="outline" className="border-gray-600 text-gray-400 text-xs">
                                +{lesson.tags.length - 4} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="ml-6">
                        <Button
                          size="sm"
                          className={
                            lesson.completed ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"
                          }
                        >
                          {lesson.completed ? (
                            <>
                              <CheckCircle className="w-4 h-4 mr-2" />
                              Review
                            </>
                          ) : (
                            <>
                              <BookOpen className="w-4 h-4 mr-2" />
                              Read
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-16 p-8 bg-red-900/10 border border-red-800/30 rounded-lg">
          <div className="flex items-start gap-4">
            <Lock className="w-6 h-6 text-red-400 mt-1" />
            <div>
              <h3 className="text-red-400 font-semibold text-lg mb-3">Security Protocol</h3>
              <p className="text-gray-300 leading-relaxed">
                {
                  "Access to these techniques is logged and monitored. These are advanced psychological methods that should be used responsibly. "
                }
                {
                  "Remember: knowledge without wisdom is dangerous. Understanding these techniques helps you recognize when they're being used against you. "
                }
                {"Use this knowledge ethically and for defensive purposes."}
              </p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

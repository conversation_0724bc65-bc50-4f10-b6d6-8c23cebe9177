import { MainLayout } from "@/widgets/layouts/main-layout"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/ui/card"
import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Badge } from "@/shared/ui/badge"
import { ArrowLeft, BookOpen, Lock, Target, Users, Brain, Eye, Zap } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { knowledgeBases } from "@/entities/knowledge-base/model/knowledge-base"

const iconMap = {
  Target,
  Users,
  Brain,
  Eye,
  Zap,
  BookOpen,
  Lock,
  Heart: Brain,
  Mask: Users,
  MessageCircle: Users,
  Radio: Zap,
}

export default function KnowledgeBasePage({ params }: { params: { id: string } }) {
  const kb = knowledgeBases.find((k) => k.id === params.id)

  if (!kb) {
    notFound()
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Intermediate":
        return "bg-yellow-900/20 text-yellow-400 border-yellow-800"
      case "Advanced":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      case "Expert":
        return "bg-red-900/20 text-red-400 border-red-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-12">
          <Link href="/">
            <Button variant="ghost" className="text-gray-400 hover:text-white mb-6">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Vault
            </Button>
          </Link>

          <div className="flex items-start justify-between mb-8">
            <div className="flex-1">
              <h1 className="text-6xl font-bold text-white mb-6">{kb.title}</h1>
              <p className="text-2xl text-gray-400 mb-8 max-w-4xl leading-relaxed">{kb.description}</p>

              <div className="flex items-center gap-8 text-lg text-gray-500 mb-8">
                <span className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  {kb.categories.length} Categories
                </span>
                <span className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  {kb.totalLessons} Total Lessons
                </span>
                <span className="flex items-center gap-2">
                  <Lock className="w-5 h-5" />
                  Classified Material
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8">Training Categories</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {kb.categories.map((category) => {
              const IconComponent = iconMap[category.icon as keyof typeof iconMap] || Target

              return (
                <Link key={category.id} href={`/knowledge-base/${kb.id}/${category.id}`}>
                  <Card className="bg-gray-900/50 border-gray-800 hover:border-red-500/50 transition-all duration-300 group cursor-pointer h-full hover-lift">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-red-900/30 to-red-800/20 flex items-center justify-center group-hover:scale-110 transition-transform">
                          <IconComponent className="w-6 h-6 text-red-400" />
                        </div>
                        <Badge className={getDifficultyColor(category.difficulty)}>{category.difficulty}</Badge>
                      </div>

                      <CardTitle className="text-white group-hover:text-red-400 transition-colors text-xl mb-3">
                        {category.title}
                      </CardTitle>
                      <CardDescription className="text-gray-400 text-base leading-relaxed mb-4">
                        {category.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent>
                      <div className="flex justify-between items-center text-sm text-gray-500">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <BookOpen className="w-4 h-4" />
                            {category.lessons.length} lessons
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </div>
        </div>

        {/* Warning Notice */}
        <div className="p-8 bg-red-900/10 border border-red-800/30 rounded-lg">
          <div className="flex items-start gap-4">
            <Lock className="w-6 h-6 text-red-400 mt-1" />
            <div>
              <h3 className="text-red-400 font-semibold text-xl mb-4">Classified Information</h3>
              <p className="text-gray-300 text-lg leading-relaxed">
                {"This knowledge vault contains advanced psychological techniques and strategic methodologies. "}
                {"These materials are provided for educational and defensive purposes only. "}
                {"Understanding these concepts helps you recognize when they're being used against you. "}
                {"Use this knowledge responsibly, ethically, and within legal boundaries."}
              </p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

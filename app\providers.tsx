"use client"

import { createContext, useState, type <PERSON>actNode } from "react"

interface SearchResult {
  type: "lesson" | "category" | "knowledgeBase"
  id: string
  title: string
  description: string
  content?: string
  tags?: string[]
  path: string
  knowledgeBase: string
  category?: string
  relevanceScore: number
}

interface SearchContextType {
  searchQuery: string
  setSearchQuery: (query: string) => void
  searchResults: SearchResult[]
  setSearchResults: (results: SearchResult[]) => void
}

export const SearchContext = createContext<SearchContextType | undefined>(undefined)

export function Providers({ children }: { children: ReactNode }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])

  return (
    <SearchContext.Provider value={{ searchQuery, setSearchQuery, searchResults, setSearchResults }}>
      {children}
    </SearchContext.Provider>
  )
}

export interface Lesson {
  id: string
  title: string
  description: string
  content: string
  duration: string
  type: "Theory" | "Practical" | "Case Study" | "Exercise"
  difficulty: "Beginner" | "Intermediate" | "Advanced" | "Expert"
  tags: string[]
  completed: boolean
  lastViewed?: Date
  isFavorite?: boolean
}

export interface Category {
  id: string
  title: string
  description: string
  lessons: Lesson[]
  difficulty: "Beginner" | "Intermediate" | "Advanced" | "Expert"
  estimatedTime: string
  icon: string
}

export interface KnowledgeBase {
  id: string
  title: string
  description: string
  categories: Category[]
  color: string
  icon: string
  totalLessons: number
}

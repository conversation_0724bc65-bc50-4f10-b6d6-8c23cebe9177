"use client"

import { useState } from "react"
import { But<PERSON> } from "@/shared/ui/button"
import { Bookmark } from "lucide-react"

interface FavoriteButtonProps {
  lessonId: string
  initialState?: boolean
}

export function FavoriteButton({ lessonId, initialState = false }: FavoriteButtonProps) {
  const [isFavorite, setIsFavorite] = useState(initialState)

  const toggleFavorite = () => {
    // In a real app, this would update the database
    setIsFavorite(!isFavorite)
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleFavorite}
      className={`p-1 ${isFavorite ? "text-red-400" : "text-gray-400 hover:text-red-400"}`}
      aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
    >
      <Bookmark className={`w-4 h-4 ${isFavorite ? "fill-current" : ""}`} />
    </Button>
  )
}

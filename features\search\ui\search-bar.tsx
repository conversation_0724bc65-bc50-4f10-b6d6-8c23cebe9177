"use client"

import { useState, useEffect } from "react"
import { Search, Filter, X } from "lucide-react"
import { Input } from "@/shared/ui/input"
import { But<PERSON> } from "@/shared/ui/button"
import { Card, CardContent } from "@/shared/ui/card"
import { useSearch } from "@/features/search/model/use-search"

export function SearchBar() {
  const { searchQuery, setSearchQuery, searchResults, setSearchResults } = useSearch()
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    if (searchQuery.length > 1) {
      // Simulate search results
      setSearchResults([])
      setIsOpen(true)
    } else {
      setSearchResults([])
      setIsOpen(false)
    }
  }, [searchQuery, setSearchResults])

  return (
    <div className="relative w-full max-w-2xl mx-auto">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          type="text"
          placeholder="Search my notes: girlfriend manipulation, emotional control..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-20 bg-gray-900/50 border-gray-700 text-white placeholder-gray-400 focus:border-red-500"
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
            <Filter className="w-4 h-4" />
          </Button>
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchQuery("")
                setIsOpen(false)
              }}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {isOpen && searchResults.length === 0 && searchQuery.length > 1 && (
        <Card className="absolute top-full mt-2 w-full bg-gray-900/95 border-gray-700 backdrop-blur-sm z-50">
          <CardContent className="p-4 text-center">
            <p className="text-gray-400 mb-2">No results found for "{searchQuery}"</p>
            <p className="text-xs text-gray-500">Try: "girlfriend manipulation", "emotional control", "love tactics"</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

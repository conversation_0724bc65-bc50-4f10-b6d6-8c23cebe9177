"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Save, Edit3, Trash2, Plus } from "lucide-react"

interface Note {
  id: string
  title: string
  content: string
  lessonId: string
  lessonTitle: string
  tags: string[]
  createdAt: Date
}

interface LessonNotesProps {
  lessonId: string
  lessonTitle: string
}

export function LessonNotes({ lessonId, lessonTitle }: LessonNotesProps) {
  const [notes, setNotes] = useState<Note[]>([
    {
      id: "1",
      title: "Key Takeaways",
      content:
        "The reciprocity principle works best when the favor is unexpected and personal. Small gestures can create disproportionate obligation.",
      lessonId,
      lessonTitle,
      tags: ["reciprocity", "key-points"],
      createdAt: new Date(Date.now() - 86400000),
    },
    {
      id: "2",
      title: "Personal Application",
      content:
        "Plan to use this with Sarah - surprise coffee delivery when she's stressed about work. Follow up with bigger request later.",
      lessonId,
      lessonTitle,
      tags: ["personal", "application", "sarah"],
      createdAt: new Date(Date.now() - 43200000),
    },
  ])

  const [isEditing, setIsEditing] = useState<string | null>(null)
  const [newNote, setNewNote] = useState({ title: "", content: "", tags: "" })
  const [showNewNote, setShowNewNote] = useState(false)

  const saveNote = (noteId: string, title: string, content: string, tags: string) => {
    const tagArray = tags
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag.length > 0)

    setNotes((prev) => prev.map((note) => (note.id === noteId ? { ...note, title, content, tags: tagArray } : note)))
    setIsEditing(null)
  }

  const addNote = () => {
    if (newNote.title.trim() && newNote.content.trim()) {
      const tagArray = newNote.tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0)

      const note: Note = {
        id: Date.now().toString(),
        title: newNote.title,
        content: newNote.content,
        lessonId,
        lessonTitle,
        tags: tagArray,
        createdAt: new Date(),
      }

      setNotes((prev) => [note, ...prev])
      setNewNote({ title: "", content: "", tags: "" })
      setShowNewNote(false)
    }
  }

  const deleteNote = (noteId: string) => {
    setNotes((prev) => prev.filter((note) => note.id !== noteId))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-red-400" />
            Personal Notes
          </h3>
          <p className="text-gray-400 text-sm">Keep track of your insights and applications</p>
        </div>
        <Button onClick={() => setShowNewNote(true)} className="bg-red-600 hover:bg-red-700 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Add Note
        </Button>
      </div>

      {/* New Note Form */}
      {showNewNote && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white text-lg">New Note</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <input
              type="text"
              placeholder="Note title..."
              value={newNote.title}
              onChange={(e) => setNewNote((prev) => ({ ...prev, title: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400"
            />
            <Textarea
              placeholder="Write your notes here..."
              value={newNote.content}
              onChange={(e) => setNewNote((prev) => ({ ...prev, content: e.target.value }))}
              className="bg-gray-800 border-gray-700 text-white placeholder-gray-400 min-h-[100px]"
            />
            <input
              type="text"
              placeholder="Tags (comma separated)..."
              value={newNote.tags}
              onChange={(e) => setNewNote((prev) => ({ ...prev, tags: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400"
            />
            <div className="flex gap-2">
              <Button onClick={addNote} className="bg-green-600 hover:bg-green-700">
                <Save className="w-4 h-4 mr-2" />
                Save Note
              </Button>
              <Button variant="outline" onClick={() => setShowNewNote(false)} className="border-gray-600 text-gray-300">
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes List */}
      <div className="space-y-4">
        {notes.map((note) => (
          <Card key={note.id} className="bg-gray-900/50 border-gray-800">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {isEditing === note.id ? (
                    <input
                      type="text"
                      defaultValue={note.title}
                      className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
                      id={`title-${note.id}`}
                    />
                  ) : (
                    <CardTitle className="text-white text-lg">{note.title}</CardTitle>
                  )}
                </div>
                <div className="flex gap-2">
                  {isEditing === note.id ? (
                    <Button
                      size="sm"
                      onClick={() => {
                        const titleInput = document.getElementById(`title-${note.id}`) as HTMLInputElement
                        const contentInput = document.getElementById(`content-${note.id}`) as HTMLTextAreaElement
                        const tagsInput = document.getElementById(`tags-${note.id}`) as HTMLInputElement
                        saveNote(note.id, titleInput.value, contentInput.value, tagsInput.value)
                      }}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Save className="w-4 h-4" />
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsEditing(note.id)}
                      className="border-gray-600 text-gray-300"
                    >
                      <Edit3 className="w-4 h-4" />
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => deleteNote(note.id)}
                    className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isEditing === note.id ? (
                <div className="space-y-4">
                  <Textarea
                    defaultValue={note.content}
                    className="bg-gray-800 border-gray-700 text-white min-h-[100px]"
                    id={`content-${note.id}`}
                  />
                  <input
                    type="text"
                    defaultValue={note.tags.join(", ")}
                    placeholder="Tags (comma separated)..."
                    className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white placeholder-gray-400"
                    id={`tags-${note.id}`}
                  />
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-gray-300 leading-relaxed">{note.content}</p>
                  <div className="flex flex-wrap gap-2">
                    {note.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="border-gray-600 text-gray-400 text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {notes.length === 0 && !showNewNote && (
        <Card className="bg-gray-900/30 border-gray-800 border-dashed">
          <CardContent className="text-center py-12">
            <BookOpen className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h4 className="text-gray-400 text-lg mb-2">No notes yet</h4>
            <p className="text-gray-500 text-sm mb-4">Start taking notes to remember key insights and applications</p>
            <Button onClick={() => setShowNewNote(true)} variant="outline" className="border-gray-600 text-gray-300">
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Note
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

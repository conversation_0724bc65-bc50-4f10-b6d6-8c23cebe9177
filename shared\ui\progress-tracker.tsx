"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, Target, Clock, BookO<PERSON>, Zap, Brain } from "lucide-react"
import { knowledgeBases } from "@/shared/data/knowledge-base"

interface UserProgress {
  totalLessons: number
  completedLessons: number
  currentStreak: number
  totalTimeSpent: number
  favoriteCategory: string
  skillLevel: string
}

export function ProgressTracker() {
  const [progress, setProgress] = useState<UserProgress>({
    totalLessons: 0,
    completedLessons: 0,
    currentStreak: 0,
    totalTimeSpent: 0,
    favoriteCategory: "Dark Psychology",
    skillLevel: "Intermediate",
  })

  useEffect(() => {
    // Calculate actual progress from knowledge base
    const totalLessons = knowledgeBases.reduce(
      (acc, kb) => acc + kb.categories.reduce((catAcc, cat) => catAcc + cat.lessons.length, 0),
      0,
    )

    const completedLessons = knowledgeBases.reduce(
      (acc, kb) =>
        acc +
        kb.categories.reduce((catAcc, cat) => catAcc + cat.lessons.filter((lesson) => lesson.completed).length, 0),
      0,
    )

    setProgress((prev) => ({
      ...prev,
      totalLessons,
      completedLessons,
      currentStreak: 7, // Mock data
      totalTimeSpent: 145, // Mock data in minutes
    }))
  }, [])

  const progressPercentage = progress.totalLessons > 0 ? (progress.completedLessons / progress.totalLessons) * 100 : 0

  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case "Beginner":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Intermediate":
        return "bg-yellow-900/20 text-yellow-400 border-yellow-800"
      case "Advanced":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      case "Expert":
        return "bg-red-900/20 text-red-400 border-red-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  const achievements = [
    { id: 1, title: "First Steps", description: "Completed your first lesson", unlocked: true },
    { id: 2, title: "Manipulation Master", description: "Completed 10 manipulation techniques", unlocked: false },
    { id: 3, title: "Psychology Expert", description: "Mastered emotional triggers", unlocked: false },
    { id: 4, title: "Relationship Guru", description: "Completed all dating tactics", unlocked: false },
  ]

  return (
    <div className="space-y-6">
      {/* Main Progress Card */}
      <Card className="bg-gradient-to-br from-red-900/10 to-red-800/5 border-red-800/30">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white text-xl">Your Progress</CardTitle>
              <CardDescription className="text-gray-300">Master the art of psychological influence</CardDescription>
            </div>
            <Badge className={getSkillLevelColor(progress.skillLevel)}>{progress.skillLevel}</Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Overall Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-300">Overall Completion</span>
              <span className="text-sm text-gray-300">{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-3" />
            <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
              <span>{progress.completedLessons} completed</span>
              <span>{progress.totalLessons} total lessons</span>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <Target className="w-6 h-6 text-red-400 mx-auto mb-2" />
              <div className="text-lg font-bold text-white">{progress.currentStreak}</div>
              <div className="text-xs text-gray-400">Day Streak</div>
            </div>

            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <Clock className="w-6 h-6 text-blue-400 mx-auto mb-2" />
              <div className="text-lg font-bold text-white">{Math.floor(progress.totalTimeSpent / 60)}h</div>
              <div className="text-xs text-gray-400">Time Spent</div>
            </div>

            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <BookOpen className="w-6 h-6 text-green-400 mx-auto mb-2" />
              <div className="text-lg font-bold text-white">{progress.completedLessons}</div>
              <div className="text-xs text-gray-400">Completed</div>
            </div>

            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <Brain className="w-6 h-6 text-purple-400 mx-auto mb-2" />
              <div className="text-lg font-bold text-white">4</div>
              <div className="text-xs text-gray-400">Categories</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card className="bg-gray-900/50 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Achievements
          </CardTitle>
          <CardDescription className="text-gray-400">Unlock rewards as you master new techniques</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {achievements.map((achievement) => (
              <div
                key={achievement.id}
                className={`p-4 rounded-lg border transition-all ${
                  achievement.unlocked ? "bg-yellow-900/10 border-yellow-800/30" : "bg-gray-800/30 border-gray-700"
                }`}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      achievement.unlocked ? "bg-yellow-600" : "bg-gray-600"
                    }`}
                  >
                    <Trophy className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className={`font-semibold ${achievement.unlocked ? "text-yellow-400" : "text-gray-400"}`}>
                      {achievement.title}
                    </h4>
                    <p className="text-sm text-gray-500">{achievement.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="bg-gray-900/50 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="w-5 h-5 text-red-400" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-gray-300 text-sm">Completed "Reciprocity Principle"</span>
              <span className="text-xs text-gray-500 ml-auto">2 hours ago</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-gray-300 text-sm">Started "Emotional Triggers"</span>
              <span className="text-xs text-gray-500 ml-auto">1 day ago</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <span className="text-gray-300 text-sm">Bookmarked "Love Bombing Techniques"</span>
              <span className="text-xs text-gray-500 ml-auto">2 days ago</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

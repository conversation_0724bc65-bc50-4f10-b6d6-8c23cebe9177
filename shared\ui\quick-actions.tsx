"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bookmark, Heart, Target, Zap, Brain, Eye, MessageCircle } from "lucide-react"

interface QuickAction {
  id: string
  title: string
  description: string
  icon: any
  category: string
  path: string
  difficulty: "Beginner" | "Intermediate" | "Advanced" | "Expert"
  tags: string[]
}

const quickActions: QuickAction[] = [
  {
    id: "girlfriend-control",
    title: "Girlfriend Emotional Control",
    description: "Master emotional triggers and dependency creation in romantic relationships",
    icon: Heart,
    category: "Relationships",
    path: "/knowledge-base/dark-psychology/emotional-manipulation/emotional-triggers",
    difficulty: "Advanced",
    tags: ["relationships", "emotional-control", "manipulation", "dating"],
  },
  {
    id: "reciprocity-dating",
    title: "Dating Reciprocity Tactics",
    description: "Use obligation and debt to build romantic attachment",
    icon: Target,
    category: "Dating",
    path: "/knowledge-base/dark-psychology/manipulation-techniques/reciprocity-principle",
    difficulty: "Intermediate",
    tags: ["reciprocity", "dating", "relationships", "influence"],
  },
  {
    id: "scarcity-romance",
    title: "Romantic Scarcity Techniques",
    description: "Create desire through unavailability and limited access",
    icon: Zap,
    category: "Romance",
    path: "/knowledge-base/dark-psychology/manipulation-techniques/scarcity-tactics",
    difficulty: "Intermediate",
    tags: ["scarcity", "romance", "dating", "desire"],
  },
  {
    id: "love-bombing-guide",
    title: "Love Bombing Mastery",
    description: "Overwhelming affection to create emotional dependency",
    icon: Brain,
    category: "Advanced",
    path: "/knowledge-base/dark-psychology/emotional-manipulation/love-bombing",
    difficulty: "Expert",
    tags: ["love-bombing", "emotional-abuse", "relationships", "control"],
  },
  {
    id: "body-language-reading",
    title: "Reading Her Emotions",
    description: "Detect hidden feelings through micro-expressions",
    icon: Eye,
    category: "Psychology",
    path: "/knowledge-base/dark-psychology/body-language-reading/micro-expressions",
    difficulty: "Intermediate",
    tags: ["body-language", "emotions", "detection", "psychology"],
  },
  {
    id: "conversation-control",
    title: "Conversation Manipulation",
    description: "Extract information and build influence through dialogue",
    icon: MessageCircle,
    category: "Communication",
    path: "/knowledge-base/social-engineering/information-elicitation/conversation-techniques",
    difficulty: "Advanced",
    tags: ["conversation", "elicitation", "influence", "communication"],
  },
]

export function QuickActions() {
  const [bookmarked, setBookmarked] = useState<string[]>([])

  const toggleBookmark = (actionId: string) => {
    setBookmarked((prev) => (prev.includes(actionId) ? prev.filter((id) => id !== actionId) : [...prev, actionId]))
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Intermediate":
        return "bg-yellow-900/20 text-yellow-400 border-yellow-800"
      case "Advanced":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      case "Expert":
        return "bg-red-900/20 text-red-400 border-red-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Quick Access Techniques</h2>
        <p className="text-gray-400">Jump directly to the most effective manipulation tactics</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {quickActions.map((action) => {
          const IconComponent = action.icon
          const isBookmarked = bookmarked.includes(action.id)

          return (
            <Card
              key={action.id}
              className="bg-gray-900/50 border-gray-800 hover:border-red-500/50 transition-all duration-300 group cursor-pointer hover-lift"
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between mb-3">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-red-900/30 to-red-800/20 flex items-center justify-center group-hover:scale-110 transition-transform">
                    <IconComponent className="w-5 h-5 text-red-400" />
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleBookmark(action.id)
                    }}
                    className={`p-1 ${isBookmarked ? "text-red-400" : "text-gray-400 hover:text-red-400"}`}
                  >
                    <Bookmark className={`w-4 h-4 ${isBookmarked ? "fill-current" : ""}`} />
                  </Button>
                </div>

                <CardTitle className="text-white group-hover:text-red-400 transition-colors text-lg leading-tight">
                  {action.title}
                </CardTitle>
                <CardDescription className="text-gray-400 text-sm leading-relaxed">
                  {action.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="flex items-center justify-between mb-3">
                  <Badge className={getDifficultyColor(action.difficulty)} variant="outline">
                    {action.difficulty}
                  </Badge>
                  <span className="text-xs text-gray-500">{action.category}</span>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {action.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="border-gray-600 text-gray-400 text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <Button
                  className="w-full bg-red-600 hover:bg-red-700 text-white"
                  onClick={() => (window.location.href = action.path)}
                >
                  Start Learning
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}

"use client"

import { useState, useEffect, useMemo } from "react"
import { Search, Filter, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible"
import { useSearch } from "@/app/providers"
import { knowledgeBases } from "@/shared/data/knowledge-base"

interface SearchResult {
  type: "lesson" | "category" | "knowledgeBase"
  id: string
  title: string
  description: string
  content?: string
  tags?: string[]
  path: string
  knowledgeBase: string
  category?: string
  relevanceScore: number
}

export function SearchBar() {
  const { searchQuery, setSearchQuery, searchResults, setSearchResults } = useSearch()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [selectedDifficulty, setSelectedDifficulty] = useState<string[]>([])
  const [isFiltersOpen, setIsFiltersOpen] = useState(false)

  // Smart tag categorization
  const tagCategories = useMemo(() => {
    const allTags = Array.from(
      new Set(
        knowledgeBases.flatMap((kb) => kb.categories.flatMap((cat) => cat.lessons.flatMap((lesson) => lesson.tags))),
      ),
    )

    return {
      manipulation: allTags.filter((tag) =>
        ["manipulation", "influence", "persuasion", "control", "gaslighting"].some((keyword) =>
          tag.toLowerCase().includes(keyword),
        ),
      ),
      psychology: allTags.filter((tag) =>
        ["psychology", "emotions", "triggers", "behavior", "cognitive"].some((keyword) =>
          tag.toLowerCase().includes(keyword),
        ),
      ),
      relationships: allTags.filter((tag) =>
        ["relationships", "love-bombing", "emotional-abuse", "dating", "romance"].some((keyword) =>
          tag.toLowerCase().includes(keyword),
        ),
      ),
      tactics: allTags.filter((tag) =>
        ["reciprocity", "scarcity", "authority", "social-proof", "commitment"].some((keyword) =>
          tag.toLowerCase().includes(keyword),
        ),
      ),
      other: allTags.filter(
        (tag) =>
          ![
            "manipulation",
            "influence",
            "persuasion",
            "control",
            "gaslighting",
            "psychology",
            "emotions",
            "triggers",
            "behavior",
            "cognitive",
            "relationships",
            "love-bombing",
            "emotional-abuse",
            "dating",
            "romance",
            "reciprocity",
            "scarcity",
            "authority",
            "social-proof",
            "commitment",
          ].some((keyword) => tag.toLowerCase().includes(keyword)),
      ),
    }
  }, [])

  const difficulties = ["Beginner", "Intermediate", "Advanced", "Expert"]

  // Smart search with relevance scoring
  const performSmartSearch = (query: string) => {
    const results: SearchResult[] = []
    const searchTerms = query
      .toLowerCase()
      .split(" ")
      .filter((term) => term.length > 1)

    knowledgeBases.forEach((kb) => {
      // Search in knowledge base
      const kbScore = calculateRelevanceScore(kb.title, kb.description, "", [], searchTerms)
      if (kbScore > 0) {
        results.push({
          type: "knowledgeBase",
          id: kb.id,
          title: kb.title,
          description: kb.description,
          path: `/knowledge-base/${kb.id}`,
          knowledgeBase: kb.title,
          relevanceScore: kbScore,
        })
      }

      kb.categories.forEach((category) => {
        // Search in categories
        const catScore = calculateRelevanceScore(category.title, category.description, "", [], searchTerms)
        if (catScore > 0) {
          results.push({
            type: "category",
            id: category.id,
            title: category.title,
            description: category.description,
            path: `/knowledge-base/${kb.id}/${category.id}`,
            knowledgeBase: kb.title,
            relevanceScore: catScore,
          })
        }

        category.lessons.forEach((lesson) => {
          const lessonScore = calculateRelevanceScore(
            lesson.title,
            lesson.description,
            lesson.content,
            lesson.tags,
            searchTerms,
          )

          const matchesTags = selectedTags.length === 0 || selectedTags.some((tag) => lesson.tags.includes(tag))
          const matchesDifficulty = selectedDifficulty.length === 0 || selectedDifficulty.includes(lesson.difficulty)

          if (lessonScore > 0 && matchesTags && matchesDifficulty) {
            results.push({
              type: "lesson",
              id: lesson.id,
              title: lesson.title,
              description: lesson.description,
              content: lesson.content.substring(0, 200) + "...",
              tags: lesson.tags,
              path: `/knowledge-base/${kb.id}/${category.id}/${lesson.id}`,
              knowledgeBase: kb.title,
              category: category.title,
              relevanceScore: lessonScore,
            })
          }
        })
      })
    })

    // Sort by relevance score
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore).slice(0, 10)
  }

  // Smart relevance scoring algorithm
  const calculateRelevanceScore = (
    title: string,
    description: string,
    content: string,
    tags: string[],
    searchTerms: string[],
  ): number => {
    let score = 0
    const titleLower = title.toLowerCase()
    const descLower = description.toLowerCase()
    const contentLower = content.toLowerCase()
    const tagsLower = tags.map((tag) => tag.toLowerCase())

    searchTerms.forEach((term) => {
      // Exact title match (highest priority)
      if (titleLower.includes(term)) score += 10

      // Description match
      if (descLower.includes(term)) score += 5

      // Tag match
      if (tagsLower.some((tag) => tag.includes(term))) score += 7

      // Content match
      if (contentLower.includes(term)) score += 2

      // Fuzzy matching for common misspellings and synonyms
      const synonyms = getSynonyms(term)
      synonyms.forEach((synonym) => {
        if (titleLower.includes(synonym)) score += 8
        if (descLower.includes(synonym)) score += 4
        if (tagsLower.some((tag) => tag.includes(synonym))) score += 6
      })
    })

    return score
  }

  // Synonym mapping for smarter search
  const getSynonyms = (term: string): string[] => {
    const synonymMap: Record<string, string[]> = {
      manipulation: ["influence", "control", "persuasion", "coercion"],
      girlfriend: ["partner", "relationship", "dating", "romance"],
      boyfriend: ["partner", "relationship", "dating", "romance"],
      love: ["romance", "affection", "emotional", "relationship"],
      control: ["manipulation", "influence", "dominance", "power"],
      psychology: ["mental", "behavioral", "cognitive", "emotional"],
      tactics: ["techniques", "methods", "strategies", "approaches"],
      emotional: ["feelings", "emotions", "psychological", "mental"],
    }

    return synonymMap[term] || []
  }

  useEffect(() => {
    if (searchQuery.length > 1) {
      const results = performSmartSearch(searchQuery)
      setSearchResults(results)
      setIsOpen(true)
    } else {
      setSearchResults([])
      setIsOpen(false)
    }
  }, [searchQuery, selectedTags, selectedDifficulty])

  const toggleTag = (tag: string) => {
    setSelectedTags((prev) => (prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]))
  }

  const toggleDifficulty = (difficulty: string) => {
    setSelectedDifficulty((prev) =>
      prev.includes(difficulty) ? prev.filter((d) => d !== difficulty) : [...prev, difficulty],
    )
  }

  const clearAllFilters = () => {
    setSelectedTags([])
    setSelectedDifficulty([])
  }

  return (
    <div className="relative w-full max-w-2xl mx-auto">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          type="text"
          placeholder="Search: girlfriend manipulation, emotional control, love tactics..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-20 bg-gray-900/50 border-gray-700 text-white placeholder-gray-400 focus:border-red-500"
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsFiltersOpen(!isFiltersOpen)}
            className={`text-gray-400 hover:text-white ${selectedTags.length > 0 || selectedDifficulty.length > 0 ? "text-red-400" : ""}`}
          >
            <Filter className="w-4 h-4" />
          </Button>
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchQuery("")
                setIsOpen(false)
              }}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {(selectedTags.length > 0 || selectedDifficulty.length > 0) && (
        <div className="mt-2 flex items-center gap-2 flex-wrap">
          <span className="text-xs text-gray-400">Active filters:</span>
          {selectedTags.map((tag) => (
            <Badge
              key={tag}
              variant="default"
              className="bg-red-600 hover:bg-red-700 text-xs cursor-pointer"
              onClick={() => toggleTag(tag)}
            >
              {tag} <X className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          {selectedDifficulty.map((difficulty) => (
            <Badge
              key={difficulty}
              variant="default"
              className="bg-red-600 hover:bg-red-700 text-xs cursor-pointer"
              onClick={() => toggleDifficulty(difficulty)}
            >
              {difficulty} <X className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-xs text-gray-400 hover:text-white h-6 px-2"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Advanced Filters */}
      <Collapsible open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
        <CollapsibleContent className="mt-4 space-y-4 p-4 bg-gray-900/30 rounded-lg border border-gray-700">
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Filter className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-medium text-gray-300">Filter by Category:</span>
            </div>

            <div className="space-y-3">
              <div>
                <span className="text-xs text-gray-400 mb-2 block">🎯 Manipulation & Influence</span>
                <div className="flex flex-wrap gap-2">
                  {tagCategories.manipulation.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className={`cursor-pointer transition-colors text-xs ${
                        selectedTags.includes(tag)
                          ? "bg-red-600 hover:bg-red-700"
                          : "border-gray-600 text-gray-300 hover:bg-gray-800"
                      }`}
                      onClick={() => toggleTag(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <span className="text-xs text-gray-400 mb-2 block">💕 Relationships & Dating</span>
                <div className="flex flex-wrap gap-2">
                  {tagCategories.relationships.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className={`cursor-pointer transition-colors text-xs ${
                        selectedTags.includes(tag)
                          ? "bg-red-600 hover:bg-red-700"
                          : "border-gray-600 text-gray-300 hover:bg-gray-800"
                      }`}
                      onClick={() => toggleTag(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <span className="text-xs text-gray-400 mb-2 block">🧠 Psychology & Behavior</span>
                <div className="flex flex-wrap gap-2">
                  {tagCategories.psychology.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className={`cursor-pointer transition-colors text-xs ${
                        selectedTags.includes(tag)
                          ? "bg-red-600 hover:bg-red-700"
                          : "border-gray-600 text-gray-300 hover:bg-gray-800"
                      }`}
                      onClick={() => toggleTag(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <span className="text-xs text-gray-400 mb-2 block">⚡ Advanced Tactics</span>
                <div className="flex flex-wrap gap-2">
                  {tagCategories.tactics.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className={`cursor-pointer transition-colors text-xs ${
                        selectedTags.includes(tag)
                          ? "bg-red-600 hover:bg-red-700"
                          : "border-gray-600 text-gray-300 hover:bg-gray-800"
                      }`}
                      onClick={() => toggleTag(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div>
            <span className="text-sm font-medium text-gray-300 mb-2 block">Difficulty Level:</span>
            <div className="flex gap-2">
              {difficulties.map((difficulty) => (
                <Badge
                  key={difficulty}
                  variant={selectedDifficulty.includes(difficulty) ? "default" : "outline"}
                  className={`cursor-pointer transition-colors ${
                    selectedDifficulty.includes(difficulty)
                      ? "bg-red-600 hover:bg-red-700"
                      : "border-gray-600 text-gray-300 hover:bg-gray-800"
                  }`}
                  onClick={() => toggleDifficulty(difficulty)}
                >
                  {difficulty}
                </Badge>
              ))}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Search Results */}
      {isOpen && searchResults.length > 0 && (
        <Card className="absolute top-full mt-2 w-full bg-gray-900/95 border-gray-700 backdrop-blur-sm z-50 max-h-96 overflow-y-auto">
          <CardContent className="p-4">
            <div className="space-y-3">
              {searchResults.map((result, index) => (
                <div
                  key={`${result.type}-${result.id}-${index}`}
                  className="p-3 rounded-lg bg-gray-800/50 hover:bg-gray-800 transition-colors cursor-pointer group"
                  onClick={() => {
                    window.location.href = result.path
                    setIsOpen(false)
                  }}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-white group-hover:text-red-400 transition-colors">
                      {result.title}
                    </h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {result.type}
                      </Badge>
                      <span className="text-xs text-gray-500">{Math.round(result.relevanceScore)}% match</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-400 mb-2">{result.description}</p>
                  {result.content && <p className="text-xs text-gray-500 mb-2">{result.content}</p>}
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {result.knowledgeBase} {result.category && `→ ${result.category}`}
                    </span>
                    {result.tags && (
                      <div className="flex gap-1">
                        {result.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {isOpen && searchResults.length === 0 && searchQuery.length > 1 && (
        <Card className="absolute top-full mt-2 w-full bg-gray-900/95 border-gray-700 backdrop-blur-sm z-50">
          <CardContent className="p-4 text-center">
            <p className="text-gray-400 mb-2">No results found for "{searchQuery}"</p>
            <p className="text-xs text-gray-500">Try: "girlfriend manipulation", "emotional control", "love tactics"</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

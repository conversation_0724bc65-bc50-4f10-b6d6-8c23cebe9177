import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title, CardDescription } from "@/shared/ui/card"
import { <PERSON>, <PERSON>, Eye } from "lucide-react"

export function FeaturedTechniques() {
  return (
    <div className="bg-gray-900/20 border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Featured Techniques</h2>
          <p className="text-gray-400">My most effective psychological strategies</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-gradient-to-br from-red-900/20 to-red-800/10 border-red-800/30 hover-lift">
            <CardHeader>
              <Target className="w-8 h-8 text-red-400 mb-2" />
              <CardTitle className="text-white">Reciprocity Mastery</CardTitle>
              <CardDescription className="text-gray-300">
                Create obligation and leverage social debt for influence
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-gradient-to-br from-purple-900/20 to-purple-800/10 border-purple-800/30 hover-lift">
            <CardHeader>
              <Brain className="w-8 h-8 text-purple-400 mb-2" />
              <CardTitle className="text-white">Emotional Triggers</CardTitle>
              <CardDescription className="text-gray-300">
                Identify and exploit emotional vulnerabilities for maximum impact
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-gradient-to-br from-orange-900/20 to-orange-800/10 border-orange-800/30 hover-lift">
            <CardHeader>
              <Eye className="w-8 h-8 text-orange-400 mb-2" />
              <CardTitle className="text-white">Reality Distortion</CardTitle>
              <CardDescription className="text-gray-300">
                Advanced techniques for altering perception and memory
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    </div>
  )
}

import { <PERSON><PERSON> } from "@/shared/ui/button"
import { <PERSON><PERSON><PERSON>, Lock, Target } from "lucide-react"
import Link from "next/link"

export function HeroSection() {
  return (
    <div className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-red-900/10 via-black to-black" />
      <div className="absolute inset-0 bg-grid opacity-20" />
      <div className="relative max-w-7xl mx-auto px-4 py-24 sm:px-6 lg:px-8">
        <div className="text-center">
          <div className="inline-flex items-center gap-2 bg-red-900/20 border border-red-800/30 rounded-full px-4 py-2 mb-8">
            <Lock className="w-4 h-4 text-red-400" />
            <span className="text-sm text-red-400">PERSONAL COLLECTION</span>
          </div>
          <h1 className="text-7xl font-bold tracking-tight mb-6">
            <span className="text-red-500">MY SECRET</span>
            <br />
            <span className="text-white">NOTEBOOK</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            {"A private collection of psychological techniques, manipulation strategies, and influence tactics. "}
            {"For personal use and educational purposes only."}
          </p>
          <div className="flex justify-center gap-4">
            <Link href="/dashboard">
              <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white px-8">
                <Target className="w-4 h-4 mr-2" />
                Go to Dashboard
              </Button>
            </Link>
            <Link href="#knowledge-bases">
              <Button size="lg" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800 px-8">
                <BookOpen className="w-4 h-4 mr-2" />
                Browse Techniques
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/shared/ui/card"
import { <PERSON>, <PERSON>, Zap, Eye } from "lucide-react"
import Link from "next/link"

export function KnowledgeBaseCards() {
  return (
    <Card className="bg-gray-900/50 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white text-2xl">Knowledge Bases</CardTitle>
        <CardDescription className="text-gray-400">Browse all categories</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link href="/knowledge-base/dark-psychology">
            <div className="p-4 bg-gradient-to-br from-red-900/20 to-red-800/10 border border-red-800/30 rounded-lg hover:bg-red-900/30 transition-colors cursor-pointer group">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 rounded-lg bg-red-900/40 flex items-center justify-center">
                  <Brain className="w-5 h-5 text-red-400" />
                </div>
                <h4 className="font-semibold text-white group-hover:text-red-400 transition-colors">Dark Psychology</h4>
              </div>
              <p className="text-sm text-gray-400">
                Understanding the darker aspects of human behavior and psychological influence
              </p>
            </div>
          </Link>

          <Link href="/knowledge-base/social-engineering">
            <div className="p-4 bg-gradient-to-br from-purple-900/20 to-purple-800/10 border border-purple-800/30 rounded-lg hover:bg-purple-900/30 transition-colors cursor-pointer group">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 rounded-lg bg-purple-900/40 flex items-center justify-center">
                  <Target className="w-5 h-5 text-purple-400" />
                </div>
                <h4 className="font-semibold text-white group-hover:text-purple-400 transition-colors">
                  Social Engineering
                </h4>
              </div>
              <p className="text-sm text-gray-400">The art of human manipulation and social influence</p>
            </div>
          </Link>

          <Link href="/knowledge-base/cognitive-warfare">
            <div className="p-4 bg-gradient-to-br from-orange-900/20 to-orange-800/10 border border-orange-800/30 rounded-lg hover:bg-orange-900/30 transition-colors cursor-pointer group">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 rounded-lg bg-orange-900/40 flex items-center justify-center">
                  <Zap className="w-5 h-5 text-orange-400" />
                </div>
                <h4 className="font-semibold text-white group-hover:text-orange-400 transition-colors">
                  Cognitive Warfare
                </h4>
              </div>
              <p className="text-sm text-gray-400">Mental strategies and psychological operations</p>
            </div>
          </Link>

          <Link href="/knowledge-base/surveillance-tactics">
            <div className="p-4 bg-gradient-to-br from-green-900/20 to-green-800/10 border border-green-800/30 rounded-lg hover:bg-green-900/30 transition-colors cursor-pointer group">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 rounded-lg bg-green-900/40 flex items-center justify-center">
                  <Eye className="w-5 h-5 text-green-400" />
                </div>
                <h4 className="font-semibold text-white group-hover:text-green-400 transition-colors">
                  Surveillance & Intelligence
                </h4>
              </div>
              <p className="text-sm text-gray-400">Information gathering and observation techniques</p>
            </div>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}

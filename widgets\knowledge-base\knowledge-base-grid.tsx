import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/shared/ui/card"
import { <PERSON><PERSON><PERSON>, Brain, Eye, Users, Zap, TrendingUp, Target } from "lucide-react"
import Link from "next/link"
import { knowledgeBases } from "@/entities/knowledge-base/model/knowledge-base"

const iconMap = {
  Brain,
  Users,
  Zap,
  Eye,
}

export function KnowledgeBaseGrid() {
  return (
    <div id="knowledge-bases" className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
      <div className="mb-12">
        <h2 className="text-4xl font-bold text-white mb-4">Knowledge Vaults</h2>
        <p className="text-xl text-gray-400">
          {"My personal collection of psychological warfare, influence tactics, and strategic knowledge."}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {knowledgeBases.map((kb) => {
          const IconComponent = iconMap[kb.icon as keyof typeof iconMap] || Brain
          return (
            <Link key={kb.id} href={`/knowledge-base/${kb.id}`}>
              <Card className="bg-gray-900/50 border-gray-800 hover:border-red-500/50 transition-all duration-300 group cursor-pointer h-full hover-lift">
                <CardHeader>
                  <div
                    className={`w-full h-40 rounded-lg bg-gradient-to-br ${kb.color} flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300 relative overflow-hidden`}
                  >
                    <div className="absolute inset-0 bg-black/20" />
                    <IconComponent className="w-16 h-16 text-white relative z-10" />
                    <div className="absolute top-4 right-4 bg-black/50 rounded-full px-2 py-1">
                      <span className="text-xs text-white">{kb.totalLessons} techniques</span>
                    </div>
                  </div>
                  <CardTitle className="text-2xl text-white group-hover:text-red-400 transition-colors mb-2">
                    {kb.title}
                  </CardTitle>
                  <CardDescription className="text-gray-400 text-base">{kb.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <BookOpen className="w-4 h-4" />
                        {kb.categories.length} Categories
                      </span>
                      <span className="flex items-center gap-1">
                        <Target className="w-4 h-4" />
                        {kb.totalLessons} Techniques
                      </span>
                    </div>
                    <TrendingUp className="w-4 h-4 text-red-500 group-hover:scale-110 transition-transform" />
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

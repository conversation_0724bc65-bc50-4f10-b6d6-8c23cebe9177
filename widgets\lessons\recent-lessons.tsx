import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/shared/ui/card"
import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Badge } from "@/shared/ui/badge"
import { BookOpen } from "lucide-react"
import Link from "next/link"

const recentLessons = [
  {
    id: "reciprocity-principle",
    title: "The Reciprocity Principle",
    category: "Manipulation Techniques",
    description: "How to use obligation and debt to influence behavior",
    difficulty: "Intermediate",
    path: "/knowledge-base/dark-psychology/manipulation-techniques/reciprocity-principle",
  },
  {
    id: "emotional-triggers",
    title: "Emotional Triggers",
    category: "Emotional Manipulation",
    description: "How to identify and exploit emotional vulnerabilities",
    difficulty: "Advanced",
    path: "/knowledge-base/dark-psychology/emotional-manipulation/emotional-triggers",
  },
  {
    id: "love-bombing",
    title: "Love Bombing Techniques",
    category: "Emotional Manipulation",
    description: "Overwhelming someone with affection to create dependency",
    difficulty: "Expert",
    path: "/knowledge-base/dark-psychology/emotional-manipulation/love-bombing",
  },
]

export function RecentLessons() {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Intermediate":
        return "bg-yellow-900/20 text-yellow-400 border-yellow-800"
      case "Advanced":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      case "Expert":
        return "bg-red-900/20 text-red-400 border-red-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  return (
    <Card className="bg-gray-900/50 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white text-2xl">Continue Learning</CardTitle>
        <CardDescription className="text-gray-400">Pick up where you left off</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentLessons.map((lesson) => (
            <Link href={lesson.path} key={lesson.id}>
              <div className="p-4 bg-gray-800/30 rounded-lg hover:bg-gray-800/50 transition-colors cursor-pointer group">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-semibold text-white group-hover:text-red-400 transition-colors">
                    {lesson.title}
                  </h4>
                  <Badge className={getDifficultyColor(lesson.difficulty)}>{lesson.difficulty}</Badge>
                </div>
                <p className="text-sm text-gray-400 mb-3">{lesson.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">{lesson.category}</span>
                  <Button size="sm" className="bg-red-600 hover:bg-red-700">
                    <BookOpen className="w-4 h-4 mr-2" />
                    Continue
                  </Button>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

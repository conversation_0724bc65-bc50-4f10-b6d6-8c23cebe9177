import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/shared/ui/card"
import { Badge } from "@/shared/ui/badge"
import { <PERSON>, Target, Zap, Brain, Eye, MessageCircle } from "lucide-react"
import { FavoriteButton } from "@/features/favorites/ui/favorite-button"
import Link from "next/link"

const quickAccess = [
  {
    id: "girlfriend-control",
    title: "Girlfriend Control",
    description: "Master emotional triggers and dependency creation",
    icon: Heart,
    path: "/knowledge-base/dark-psychology/emotional-manipulation/emotional-triggers",
    difficulty: "Advanced",
    tags: ["relationships", "emotional-control", "manipulation"],
  },
  {
    id: "dating-tactics",
    title: "Dating Tactics",
    description: "Use reciprocity to build romantic attachment",
    icon: Target,
    path: "/knowledge-base/dark-psychology/manipulation-techniques/reciprocity-principle",
    difficulty: "Intermediate",
    tags: ["reciprocity", "dating", "relationships"],
  },
  {
    id: "scarcity-techniques",
    title: "Scarcity Techniques",
    description: "Create desire through unavailability",
    icon: <PERSON>ap,
    path: "/knowledge-base/dark-psychology/manipulation-techniques/scarcity-tactics",
    difficulty: "Intermediate",
    tags: ["scarcity", "romance", "dating"],
  },
  {
    id: "love-bombing",
    title: "Love Bombing",
    description: "Overwhelming affection to create dependency",
    icon: Brain,
    path: "/knowledge-base/dark-psychology/emotional-manipulation/love-bombing",
    difficulty: "Expert",
    tags: ["love-bombing", "emotional-abuse", "relationships"],
  },
  {
    id: "reading-emotions",
    title: "Reading Emotions",
    description: "Detect hidden feelings through micro-expressions",
    icon: Eye,
    path: "/knowledge-base/dark-psychology/body-language-reading/micro-expressions",
    difficulty: "Intermediate",
    tags: ["body-language", "emotions", "detection"],
  },
  {
    id: "conversation-control",
    title: "Conversation Control",
    description: "Extract information through dialogue",
    icon: MessageCircle,
    path: "/knowledge-base/social-engineering/information-elicitation/conversation-techniques",
    difficulty: "Advanced",
    tags: ["conversation", "elicitation", "influence"],
  },
]

export function QuickAccessGrid() {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-900/20 text-green-400 border-green-800"
      case "Intermediate":
        return "bg-yellow-900/20 text-yellow-400 border-yellow-800"
      case "Advanced":
        return "bg-orange-900/20 text-orange-400 border-orange-800"
      case "Expert":
        return "bg-red-900/20 text-red-400 border-red-800"
      default:
        return "bg-gray-900/20 text-gray-400 border-gray-800"
    }
  }

  return (
    <Card className="bg-gray-900/50 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white text-2xl">Quick Access</CardTitle>
        <CardDescription className="text-gray-400">Your most important techniques</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickAccess.map((item) => {
            const IconComponent = item.icon
            return (
              <Link href={item.path} key={item.id}>
                <div className="p-4 bg-gray-800/30 rounded-lg hover:bg-gray-800/50 transition-colors cursor-pointer group h-full">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-red-900/30 to-red-800/20 flex items-center justify-center group-hover:scale-110 transition-transform">
                        <IconComponent className="w-5 h-5 text-red-400" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-white group-hover:text-red-400 transition-colors">
                          {item.title}
                        </h4>
                        <p className="text-sm text-gray-400 mt-1">{item.description}</p>
                      </div>
                    </div>
                    <FavoriteButton lessonId={item.id} initialState={true} />
                  </div>
                  <div className="flex flex-wrap gap-2 mt-3">
                    <Badge className={getDifficultyColor(item.difficulty)}>{item.difficulty}</Badge>
                    {item.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="outline" className="border-gray-600 text-gray-400 text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

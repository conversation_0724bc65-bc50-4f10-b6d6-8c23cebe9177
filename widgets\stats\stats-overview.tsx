import { knowledgeBases } from "@/entities/knowledge-base/model/knowledge-base"

export function StatsOverview() {
  const totalLessons = knowledgeBases.reduce((acc, kb) => acc + kb.totalLessons, 0)
  const totalCategories = knowledgeBases.reduce((acc, kb) => acc + kb.categories.length, 0)

  return (
    <div className="bg-gray-900/30 border-y border-gray-800">
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="group">
            <div className="text-3xl font-bold text-red-500 mb-2 group-hover:scale-110 transition-transform">
              {knowledgeBases.length}
            </div>
            <div className="text-gray-400 text-sm">Knowledge Vaults</div>
          </div>
          <div className="group">
            <div className="text-3xl font-bold text-red-500 mb-2 group-hover:scale-110 transition-transform">
              {totalCategories}
            </div>
            <div className="text-gray-400 text-sm">Categories</div>
          </div>
          <div className="group">
            <div className="text-3xl font-bold text-red-500 mb-2 group-hover:scale-110 transition-transform">
              {totalLessons}
            </div>
            <div className="text-gray-400 text-sm">Total Techniques</div>
          </div>
          <div className="group">
            <div className="text-3xl font-bold text-red-500 mb-2 group-hover:scale-110 transition-transform">∞</div>
            <div className="text-gray-400 text-sm">Possibilities</div>
          </div>
        </div>
      </div>
    </div>
  )
}
